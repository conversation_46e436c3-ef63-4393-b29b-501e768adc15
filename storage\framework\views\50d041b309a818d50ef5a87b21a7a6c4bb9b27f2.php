<h1>Form Barang Keluar</h1>

<?php if(session('error')): ?>
    <div class="alert alert-danger">
        <?php echo e(session('error')); ?>

    </div>
<?php endif; ?>

<form action="<?php echo e(route('barang-keluar.store')); ?>" method="POST">
    <?php echo csrf_field(); ?>
    <div>
        <label for="item_id">Pilih <PERSON>:</label>
        <select name="item_id" id="item_id" required>
            <option value="">-- <PERSON><PERSON>h <PERSON> --</option>
            <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($item->id); ?>">
                    <?php echo e($item->name); ?> (Stok: <?php echo e($item->quantity); ?>)
                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
    </div>
    <br>
    <div>
        <label for="quantity_out">Jumlah Keluar:</label>
        <input type="number" name="quantity_out" id="quantity_out" min="1" required>
    </div>
    <br>
    <div>
        <label for="transaction_date">Tanggal Keluar:</label>
        <input type="date" name="transaction_date" id="transaction_date" value="<?php echo e(date('Y-m-d')); ?>" required>
    </div>
    <br>
    <div>
        <label for="notes">Keterangan:</label>
        <textarea name="notes" id="notes" rows="3"></textarea>
    </div>
    <br>
    <button type="submit">Simpan Transaksi</button>
</form>
<?php /**PATH C:\HARPROJECT\belajar\inventory barang\resources\views/transactions/create.blade.php ENDPATH**/ ?>
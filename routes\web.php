<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ClassroomController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\ItemTransactionController;
// Tambahkan ini untuk ItemLoanController
use App\Http\Controllers\ItemLoanController;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Route hanya untuk user yang sudah login
Route::middleware(['auth'])->group(function () {
    Route::resource('items', ItemController::class);
    Route::resource('loans', ItemLoanController::class);

    // PINDAHKAN ROUTE BARANG KELUAR KE SINI
    Route::get('/barang-keluar/create', [ItemTransactionController::class, 'create'])->name('barang-keluar.create');
    Route::post('/barang-keluar', [ItemTransactionController::class, 'store'])->name('barang-keluar.store');
    Route::get('/barang-keluar', [ItemTransactionController::class, 'index'])->name('barang-keluar.index');
});

// Route hanya untuk admin
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    Route::resource('categories', CategoryController::class);
    Route::resource('classrooms', ClassroomController::class);

    // Route untuk pengembalian barang
    Route::get('/loans/{loan}/return', [ItemLoanController::class, 'returnItem'])->name('loans.return');
    Route::post('/loans/{loan}/process-return', [ItemLoanController::class, 'processReturn'])->name('loans.process-return');
});

// Route untuk login, logout, register (di luar auth)
Route::get('/login', [LoginController::class, 'login'])->name('login');
Route::post('/login', [LoginController::class, 'proseslogin'])->name('proseslogin');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

Route::get('/register', [LoginController::class, 'register'])->name('register');
Route::post('/register', [LoginController::class, 'prosesRegister'])->name('prosesregister');

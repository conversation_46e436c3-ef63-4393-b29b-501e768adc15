@extends('layouts.app')

@section('content')
<div class="container">
    <div class="mb-3">
        <a href="{{ route('categories.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> <PERSON><PERSON><PERSON>
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Detail Kategori</h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <strong>Nama Kategori:</strong>
                <p>{{ $category->name }}</p>
            </div>
            <div class="mb-3">
                <strong>Des<PERSON>ripsi:</strong>
                <p>{{ $category->description ?? '-' }}</p>
            </div>
            <div class="mb-3">
                <strong>Dibuat pada:</strong>
                <p>{{ $category->created_at->format('d/m/Y H:i') }}</p>
            </div>
            <div class="mb-3">
                <strong>Terakhir diupdate:</strong>
                <p>{{ $category->updated_at->format('d/m/Y H:i') }}</p>
            </div>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">Daftar Barang dalam Kategori Ini</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="bg-light">
                        <tr>
                            <th>No</th>
                            <th>Kode</th>
                            <th>Nama Barang</th>
                            <th>Ruang Kelas</th>
                            <th>Jumlah</th>
                            <th>Kondisi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($category->items as $key => $item)
                        <tr>
                            <td>{{ $key + 1 }}</td>
                            <td>{{ $item->code }}</td>
                            <td>{{ $item->name }}</td>
                            <td>{{ $item->classroom->name }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>
                                @if($item->condition == 'Baik')
                                    <span class="badge bg-success">{{ $item->condition }}</span>
                                @elseif($item->condition == 'Rusak Ringan')
                                    <span class="badge bg-warning">{{ $item->condition }}</span>
                                @else
                                    <span class="badge bg-danger">{{ $item->condition }}</span>
                                @endif
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">Tidak ada barang dalam kategori ini</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
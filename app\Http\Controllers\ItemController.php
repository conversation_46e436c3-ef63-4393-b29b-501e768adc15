<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\Category;
use App\Models\Classroom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ItemController extends Controller
{
    public function index()
    {
        $items = Item::with(['category', 'classroom'])->get();
        $classrooms = Classroom::all();
        $categories = Category::all();
        return view('items.index', compact('items', 'categories','classrooms'));
    }

    public function create()
    {
        $categories = Category::all();
        $classrooms = Classroom::all();
        return view('items.create', compact('categories', 'classrooms'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:items',
            'category_id' => 'required|exists:categories,id',
            'classroom_id' => 'required|exists:classrooms,id',
            'description' => 'nullable|string',
            'quantity' => 'required|integer|min:0',
            'purchase_date' => 'nullable|date',
            'condition' => 'required|string|in:<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Rusak Berat',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('storage/items');
            $data['image'] = str_replace('storage/', '', $imagePath);
        }

        Item::create($data);

        return redirect()->route('items.index')
            ->with('success', 'Barang berhasil ditambahkan');
    }

    public function show(Item $item)
    {
        return view('items.show', compact('item'));
    }

    public function edit(Item $item)
    {
        $categories = Category::all();
        $classrooms = Classroom::all();
        return view('items.edit', compact('item', 'categories', 'classrooms'));
    }

    public function update(Request $request, Item $item)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:items,code,' . $item->id,
            'category_id' => 'required|exists:categories,id',
            'classroom_id' => 'required|exists:classrooms,id',
            'description' => 'nullable|string',
            'quantity' => 'required|integer|min:0',
            'purchase_date' => 'nullable|date',
            'condition' => 'required|string|in:Baik,Rusak Ringan,Rusak Berat',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            // Hapus gambar lama jika ada
            if ($item->image) {
                Storage::delete('public/' . $item->image);
            }
            
            $imagePath = $request->file('image')->store('storage/items');
            $data['image'] = str_replace('storage/', '', $imagePath);
        }

        $item->update($data);

        return redirect()->route('items.index')
            ->with('success', 'Barang berhasil diperbarui');
    }

    public function destroy(Item $item)
    {
        // Hapus gambar terkait jika ada
        if ($item->image) {
            Storage::delete('public/' . $item->image);
        }
        
        $item->delete();

        return redirect()->route('items.index')
            ->with('success', 'Barang berhasil dihapus');
    }
}
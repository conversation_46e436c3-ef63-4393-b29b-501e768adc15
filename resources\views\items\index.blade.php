@extends('layouts.app')

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Daftar Barang</h2>
        @auth
            @if (auth()->user()->role === 'admin')
        <div>
            <a href="{{ route('items.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Barang
            </a>
            <button id="export-pdf" class="btn btn-danger ml-2">
                <i class="fas fa-file-pdf"></i> Ekspor PDF
            </button>
        </div>
            @endif
        @endauth
    </div>

    @auth
        @if (auth()->user()->role === 'admin')
    <!-- Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Filter Barang</h6>
            <button id="reset-filters" class="btn btn-sm btn-secondary">
                <i class="fas fa-sync-alt"></i> Menyegarkan Filter
            </button>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="category-filter">Kategori</label>
                        <select id="category-filter" class="form-control">
                            <option value="">Semua Kategori</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->name }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="classroom-filter">Ruang Kelas</label>
                        <select id="classroom-filter" class="form-control">
                            <option value="">Semua Ruang</option>
                            @foreach($classrooms as $classroom)
                                <option value="{{ $classroom->name }}">{{ $classroom->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="condition-filter">Kondisi</label>
                        <select id="condition-filter" class="form-control">
                            <option value="">Semua Kondisi</option>
                            <option value="Baik">Baik</option>
                            <option value="Rusak Ringan">Rusak Ringan</option>
                            <option value="Rusak Berat">Rusak Berat</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="search-input">Pencarian</label>
                        <input type="text" id="search-input" class="form-control" placeholder="Cari berdasarkan nama, kode, atau lainnya...">
                    </div>
                </div>
            </div>
        </div>
    </div>
        @endif
    @endauth

    <!-- Table Section -->
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="items-table">
                    <thead class="bg-light">
                        <tr>
                            <th>No</th>
                            <th>Kode</th>
                            <th>Nama Barang</th>
                            <th>Kategori</th>
                            <th>Ruang Kelas</th>
                            <th>Jumlah</th>
                            <th>Kondisi</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($items as $key => $item)
                        <tr>
                            <td>{{ $key + 1 }}</td>
                            <td>{{ $item->code }}</td>
                            <td>{{ $item->name }}</td>
                            <td>{{ $item->category->name }}</td>
                            <td>{{ $item->classroom->name }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>
                                @if($item->condition == 'Baik')
                                    <span class="badge bg-success">{{ $item->condition }}</span>
                                @elseif($item->condition == 'Rusak Ringan')
                                    <span class="badge bg-warning">{{ $item->condition }}</span>
                                @else
                                    <span class="badge bg-danger">{{ $item->condition }}</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('items.show', $item) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(auth()->user()->role == 'admin')
                                    <a href="{{ route('items.edit', $item) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('items.destroy', $item) }}" method="POST" onsubmit="return confirm('Anda yakin ingin menghapus barang ini?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css" rel="stylesheet">
@endsection

@section('scripts')
<!-- DataTables & Buttons -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<!-- AutoTable -->  
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>

<script>
    const userRole = "{{ auth()->user()->role ?? '' }}";
</script>

<script>
$(document).ready(function() {
    // Role dari backend
    const userRole = "{{ auth()->user()->role ?? '' }}";

    // Tentukan tombol export hanya jika admin
    let buttons = [];
    if (userRole === 'admin') {
        buttons = [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6]
                }
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6]
                },
                customize: function(doc) {
                    doc.content[1].table.widths = 
                        Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                }
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> Print',
                className: 'btn btn-info',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6]
                }
            }
        ];
    }

    // Inisialisasi DataTable dengan tombol sesuai role
    var table = $('#items-table').DataTable({
        dom: '<"top"Bf>rt<"bottom"lip><"clear">',
        buttons: buttons,  // tombol dari variabel
        language: {
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ barang per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ barang",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 barang",
            infoFiltered: "(disaring dari _MAX_ total barang)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        },
        columnDefs: [
            { orderable: false, targets: [7] }
        ]
    });

    // Filter Kategori
    $('#category-filter').on('change', function() {
        table.column(3).search(this.value).draw();
    });

    // Filter Ruang Kelas
    $('#classroom-filter').on('change', function() {
        table.column(4).search(this.value).draw();
    });

    // Filter Kondisi
    $('#condition-filter').on('change', function() {
        table.column(6).search(this.value).draw();
    });

    // Pencarian Umum
    $('#search-input').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Reset semua filter
    $('#reset-filters').on('click', function() {
        $('#category-filter, #classroom-filter, #condition-filter').val('');
        $('#search-input').val('');
        table.search('').columns().search('').draw();
    });

    // Export PDF dengan AutoTable
    $('#export-pdf').on('click', function() {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();
        
        doc.autoTable({
            head: [['No', 'Kode', 'Nama Barang', 'Kategori', 'Ruang Kelas', 'Jumlah', 'Kondisi']],
            body: table.data()
                .toArray()
                .map(row => [row[0], row[1], row[2], row[3], row[4], row[5], row[6]]),
            theme: 'grid',
            headStyles: {
                fillColor: [41, 128, 185],
                textColor: 255
            },
            styles: {
                cellPadding: 3,
                fontSize: 10
            },
            margin: { top: 10 }
        });
        
        doc.save('daftar-barang.pdf');
    });
});
</script>
@endsection
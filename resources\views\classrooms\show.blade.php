@extends('layouts.app')

@section('content')
<div class="container">
    <div class="mb-3">
        <a href="{{ route('classrooms.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Ke<PERSON>li
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Detail <PERSON></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <strong>Nama <PERSON>:</strong>
                        <p>{{ $classroom->name }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Gedung:</strong>
                        <p>{{ $classroom->building ?? '-' }}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <strong>Lantai:</strong>
                        <p>{{ $classroom->floor ?? '-' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Kapasitas:</strong>
                        <p>{{ $classroom->capacity ?? '-' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-end">
        <a href="{{ route('classrooms.edit', $classroom) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit"></i> Edit
        </a>
        <form action="{{ route('classrooms.destroy', $classroom) }}" method="POST" onsubmit="return confirm('Anda yakin ingin menghapus ruang kelas ini?')">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash"></i> Hapus
            </button>
        </form>
    </div>
</div>
@endsection
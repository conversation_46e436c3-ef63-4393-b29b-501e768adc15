<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemLoan extends Model
{
    use HasFactory;

    protected $fillable = [
        'classroom_id', 'item_id', 'quantity', 'loan_date',
        'return_date', 'actual_return_date', 'status', 'notes'
    ];

    protected $casts = [
        'loan_date' => 'date',
        'return_date' => 'date',
        'actual_return_date' => 'date',
    ];

    public function classroom()
{
    return $this->belongsTo(Classroom::class);
}

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function user(){
        return $this->belongsTo(User::class);
    }
}

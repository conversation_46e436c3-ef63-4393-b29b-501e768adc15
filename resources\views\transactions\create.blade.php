<h1>Form Barang Keluar</h1>

@if(session('error'))
    <div class="alert alert-danger">
        {{ session('error') }}
    </div>
@endif

<form action="{{ route('barang-keluar.store') }}" method="POST">
    @csrf
    <div>
        <label for="item_id"><PERSON><PERSON><PERSON>:</label>
        <select name="item_id" id="item_id" required>
            <option value="">-- <PERSON><PERSON><PERSON> --</option>
            @foreach($items as $item)
                <option value="{{ $item->id }}">
                    {{ $item->name }} (Stok: {{ $item->quantity }})
                </option>
            @endforeach
        </select>
    </div>
    <br>
    <div>
        <label for="quantity_out">Jum<PERSON> Keluar:</label>
        <input type="number" name="quantity_out" id="quantity_out" min="1" required>
    </div>
    <br>
    <div>
        <label for="transaction_date">Tanggal Keluar:</label>
        <input type="date" name="transaction_date" id="transaction_date" value="{{ date('Y-m-d') }}" required>
    </div>
    <br>
    <div>
        <label for="notes">Keterangan:</label>
        <textarea name="notes" id="notes" rows="3"></textarea>
    </div>
    <br>
    <button type="submit">Simpan Transaksi</button>
</form>

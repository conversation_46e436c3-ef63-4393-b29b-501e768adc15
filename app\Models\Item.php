<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Item extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'code', 'category_id', 'classroom_id', 'description',
        'quantity', 'purchase_date', 'condition', 'image'
    ];

    protected $casts = [
        'purchase_date' => 'date',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function classroom()
    {
        return $this->belongsTo(Classroom::class);
    }

    public function loans()
    {
        return $this->hasMany(ItemLoan::class);
    }
}

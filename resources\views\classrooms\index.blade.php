@extends('layouts.app')

@section('styles')
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
@endsection

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Daftar <PERSON><PERSON> Kelas</h2>
        <a href="{{ route('classrooms.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Ruangan
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <!-- Tanpa .table-responsive agar tidak bisa digeser -->
            <table class="table table-bordered table-hover" id="classrooms-table">
                <thead class="bg-light">
                    <tr>
                        <th>No</th>
                        <th><PERSON><PERSON></th>
                        <th>Gedung</th>
                        <th>Lantai</th>
                        <th><PERSON><PERSON><PERSON></th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($classrooms as $key => $classroom)
                    <tr>
                        <td>{{ $key + 1 }}</td>
                        <td>{{ $classroom->name }}</td>
                        <td>{{ $classroom->building ?? '-' }}</td>
                        <td>{{ $classroom->floor ?? '-' }}</td>
                        <td>{{ $classroom->capacity ?? '-' }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('classrooms.show', $classroom) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('classrooms.edit', $classroom) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('classrooms.destroy', $classroom) }}" method="POST" onsubmit="return confirm('Anda yakin ingin menghapus ruang kelas ini?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center">Tidak ada data ruangan</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@section('scripts')
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#classrooms-table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/id.json'
                },
                scrollX: false,
                // Tampilkan show entries (lengthMenu) di kiri dan search di kanan
                dom:
                    '<"row mb-3"' +
                        '<"col-sm-6"l>' +   // length menu (show entries)
                        '<"col-sm-6 d-flex justify-content-end"f>' + // search box ke kanan
                    '>' +
                    't' +
                    '<"row mt-3"' +
                        '<"col-sm-5"i>' +  // info
                        '<"col-sm-7"p>' +  // pagination
                    '>'
            });
        });
    </script>
@endsection

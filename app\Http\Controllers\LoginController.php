<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class LoginController extends Controller
{
    public function login() {
        return view('login');
    }

    public function proseslogin(Request $request)
    {
        $credentials = $request->only('email', 'password');

    if (Auth::attempt($credentials)) {
        $user = Auth::user();

        if ($user->role === 'peminjam') {
            return redirect()->route('items.index');
        }

        return redirect()->route('dashboard');
    }

        return back()->withErrors(['email' => 'Email atau kata sandi salah.']);
    }

    public function logout()
    {
        Auth::logout();
        return redirect()->route('login');
    }

    public function register() {
        return view('register');
    }

    public function prosesRegister(Request $request)
    {
        $request->validate([
            'email' => 'required',
            'password' => 'required',
        ]);

        User::create([
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

    return redirect()->route('login');
    }
}

<h1>Riwayat Barang Keluar</h1>

<?php if(session('success')): ?>
    <div class="alert alert-success">
        <?php echo e(session('success')); ?>

    </div>
<?php endif; ?>

<a href="<?php echo e(route('barang-keluar.create')); ?>">Buat Transaksi Baru</a>
<br><br>
<table border="1">
    <thead>
        <tr>
            <th>Tanggal</th>
            <th><PERSON>a Barang</th>
            <th>Jumlah</th>
            <th>Petugas</th>
            <th>Catatan</th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td><?php echo e($transaction->transaction_date); ?></td>
            <td><?php echo e($transaction->item->name); ?></td>
            <td><?php echo e($transaction->quantity_out); ?></td>
            <td><?php echo e($transaction->user->name); ?></td>
            <td><?php echo e($transaction->notes); ?></td>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>
<?php /**PATH C:\HARPROJECT\belajar\inventory barang\resources\views/transactions/index.blade.php ENDPATH**/ ?>
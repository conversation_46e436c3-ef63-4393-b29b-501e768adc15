<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\ItemLoan;
use App\Models\Category;
use App\Models\Classroom;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $totalItems = Item::sum('quantity');
        $totalCategories = Category::count();
        $totalClassrooms = Classroom::count();
        $activeLoans = ItemLoan::where('status', 'Dipinjam')->count();
        $lateReturns = ItemLoan::where('status', 'Dipinjam')
                        ->whereDate('return_date', '<', now())
                        ->count();
        
        // Barang dengan stok rendah
        $lowStockItems = Item::where('quantity', '<', 5)->get();
        
        // Peminjaman terbaru
        $recentLoans = ItemLoan::with(['user', 'item'])
                        ->orderBy('created_at', 'desc')
                        ->limit(5)
                        ->get();
        
        return view('dashboard', compact('totalItems', 'totalCategories', 'totalClassrooms', 
                                        'activeLoans', 'lateReturns', 'lowStockItems', 'recentLoans'));
    }
}
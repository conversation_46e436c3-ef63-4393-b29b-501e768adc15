@extends('layouts.app')

@section('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
@endsection

@section('content')
<div class="container">
    <div class="mb-3">
        <a href="{{ route('items.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="row">
        <!-- Kartu barang -->
        <div class="col-md-4">
            <div class="card shadow mb-4">
                @if($item->image)
                    <img src="{{ asset('storage/' . $item->image) }}" class="card-img-top" alt="{{ $item->name }}">
                @else
                    <div class="bg-light text-center py-5">
                        <i class="fas fa-box fa-5x text-secondary"></i>
                        <p class="mt-3">Tidak ada gambar</p>
                    </div>
                @endif
                <div class="card-body">
                    <h4 class="card-title">{{ $item->name }}</h4>
                    <p class="card-text">
                        <span class="badge bg-primary">{{ $item->category->name }}</span>
                        @if($item->condition == 'Baik')
                            <span class="badge bg-success">{{ $item->condition }}</span>
                        @elseif($item->condition == 'Rusak Ringan')
                            <span class="badge bg-warning">{{ $item->condition }}</span>
                        @else
                            <span class="badge bg-danger">{{ $item->condition }}</span>
                        @endif
                    </p>
                    <p class="card-text">{{ $item->description ?? 'Tidak ada deskripsi' }}</p>
                </div>
            </div>
        </div>

        <!-- Detail barang -->
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Detail Barang</h5>
                </div>
                <div class="card-body">
                    @php
                        $details = [
                            'Kode Barang' => $item->code,
                            'Kategori' => $item->category->name,
                            'Ruang Kelas' => $item->classroom->name,
                            'Jumlah' => $item->quantity,
                            'Kondisi' => $item->condition,
                            'Tanggal Pembelian' => $item->purchase_date ? $item->purchase_date->format('d/m/Y') : '-',
                            'Dibuat pada' => $item->created_at->format('d/m/Y H:i'),
                            'Terakhir diupdate' => $item->updated_at->format('d/m/Y H:i'),
                        ];
                    @endphp

                    @foreach($details as $label => $value)
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">{{ $label }}</div>
                            <div class="col-md-8">
                                @if($label == 'Kondisi')
                                    @if($value == 'Baik')
                                        <span class="badge bg-success">{{ $value }}</span>
                                    @elseif($value == 'Rusak Ringan')
                                        <span class="badge bg-warning">{{ $value }}</span>
                                    @else
                                        <span class="badge bg-danger">{{ $value }}</span>
                                    @endif
                                @else
                                    {{ $value }}
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
                @if(auth()->user()->role == 'admin')
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('items.edit', $item) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <form action="{{ route('items.destroy', $item) }}" method="POST" onsubmit="return confirm('Anda yakin ingin menghapus barang ini?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </form>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Riwayat Peminjaman -->
    <div class="card shadow mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">Riwayat Peminjaman (Terbaru)</h5>
        </div>
        <div class="card-body">
            <!-- Hapus table-responsive agar tidak bisa digeser -->
            <table class="table table-bordered table-hover" id="loan-history-table">
                <thead class="bg-light">
                    <tr>
                        <th>No</th>
                        <th>Peminjam</th>
                        <th>Jumlah</th>
                        <th>Tanggal Pinjam</th>
                        <th>Tanggal Kembali</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($item->loans as $key => $loan)
                    <tr>
                        <td>{{ $key + 1 }}</td>
                        <td>{{ $loan->classroom->name }}</td>
                        <td>{{ $loan->quantity }}</td>
                        <td>{{ $loan->loan_date->format('d/m/Y') }}</td>
                        <td>{{ $loan->return_date->format('d/m/Y') }}</td>
                        <td>
                            @if($loan->status == 'Dipinjam')
                                <span class="badge bg-info">{{ $loan->status }}</span>
                            @elseif($loan->status == 'Dikembalikan')
                                <span class="badge bg-success">{{ $loan->status }}</span>
                            @else
                                <span class="badge bg-warning">{{ $loan->status }}</span>
                            @endif
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="text-center">Tidak ada riwayat peminjaman</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- DataTables -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function () {
        $('#loan-history-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/id.json'
            },
            scrollX: false,
            dom:
                '<"row mb-3"' +
                    '<"col-sm-6"l>' +  // show entries
                    '<"col-sm-6 d-flex justify-content-end"f>' + // search kanan
                '>' +
                't' +
                '<"row mt-3"' +
                    '<"col-sm-5"i>' +
                    '<"col-sm-7"p>' +
                '>'
        });
    });
</script>
@endsection

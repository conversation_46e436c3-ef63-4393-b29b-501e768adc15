

<?php $__env->startSection('styles'); ?>
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Daftar Kategori</h2>
        <a href="<?php echo e(route('categories.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Kategori
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <!-- Hapus .table-responsive agar tidak scroll horizontal -->
            <table class="table table-bordered table-hover" id="categories-table">
                <thead class="bg-light">
                    <tr>
                        <th>No</th>
                        <th><PERSON><PERSON></th>
                        <th><PERSON><PERSON><PERSON><PERSON></th>
                        <th><PERSON><PERSON><PERSON></th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e($key + 1); ?></td>
                        <td><?php echo e($category->name); ?></td>
                        <td><?php echo e($category->description ?? '-'); ?></td>
                        <td><?php echo e($category->items->count()); ?></td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('categories.show', $category)); ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('categories.edit', $category)); ?>" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('categories.destroy', $category)); ?>" method="POST" onsubmit="return confirm('Anda yakin ingin menghapus kategori ini?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="5" class="text-center">Tidak ada data kategori</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#categories-table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/id.json'
                },
                scrollX: false,
                dom:
                    '<"row mb-3"' +
                        '<"col-sm-6"l>' +   // Show entries (length menu)
                        '<"col-sm-6 d-flex justify-content-end"f>' + // Search box kanan
                    '>' +
                    't' +
                    '<"row mt-3"' +
                        '<"col-sm-5"i>' +  // Info
                        '<"col-sm-7"p>' +  // Pagination
                    '>'
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\HARPROJECT\belajar\inventory barang\resources\views/categories/index.blade.php ENDPATH**/ ?>
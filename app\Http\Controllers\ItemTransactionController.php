<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\ItemTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ItemTransactionController extends Controller
{
    // Menampilkan halaman formulir untuk membuat transaksi baru
    public function create()
    {
        $items = Item::orderBy('name')->get(); // Ambil semua item untuk dropdown
        return view('transactions.create', compact('items'));
    }

    // Menyimpan data transaksi
    public function store(Request $request)
    {
        // 1. Validasi Input
        $request->validate([
            'item_id' => 'required|exists:items,id',
            'quantity_out' => 'required|integer|min:1',
            'transaction_date' => 'required|date',
        ]);

        try {
            // Gunakan DB Transaction untuk memastikan data konsisten
            DB::transaction(function () use ($request) {
                $item = Item::findOrFail($request->item_id);

                // 2. <PERSON>k apakah stok mencukupi
                if ($item->quantity < $request->quantity_out) {
                    // Jika tidak cukup, lemparkan error
                    throw new \Exception('Stok barang tidak mencukupi.');
                }

                // 3. Kurangi stok di tabel items
                $item->quantity -= $request->quantity_out;
                $item->save();

                // 4. Catat transaksi di tabel item_transactions
                ItemTransaction::create([
                    'item_id' => $request->item_id,
                    'user_id' => Auth::id(), // Ambil ID user yang sedang login
                    'quantity_out' => $request->quantity_out,
                    'transaction_date' => $request->transaction_date,
                    'notes' => $request->notes,
                ]);
            });
        } catch (\Exception $e) {
            // Jika terjadi error, kembalikan ke halaman sebelumnya dengan pesan error
            return back()->with('error', $e->getMessage());
        }

        // Jika berhasil, kembalikan dengan pesan sukses
        return redirect()->route('barang-keluar.index')->with('success', 'Transaksi barang keluar berhasil dicatat.');
    }

    // Menampilkan riwayat transaksi
    public function index()
    {
        $transactions = ItemTransaction::with(['item', 'user'])->latest()->get();
        return view('transactions.index', compact('transactions'));
    }
}
